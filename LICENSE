MIT License

Copyright (c) 2024 WeChat Spider Project

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

---

## 免责声明

本软件仅供学习和研究使用。使用者需要：

1. **遵守法律法规**：确保使用本软件的行为符合当地法律法规
2. **遵守平台规则**：遵守微信公众平台的服务条款和使用协议
3. **合理使用**：不得用于商业用途或其他可能侵犯他人权益的行为
4. **承担责任**：使用者需自行承担使用本软件可能产生的任何风险和责任

开发者不对以下情况承担任何责任：
- 因使用本软件导致的账号封禁或其他损失
- 因违反相关法律法规或平台规则产生的法律后果
- 因软件缺陷或错误导致的数据丢失或其他损失
- 因网络环境或第三方服务变化导致的功能异常

## 使用建议

1. **频率控制**：合理控制抓取频率，避免对目标服务器造成过大压力
2. **数据保护**：妥善保管抓取的数据，不要泄露他人隐私信息
3. **及时更新**：定期更新软件版本，获取最新的功能和安全修复
4. **备份数据**：定期备份重要的配置文件和抓取结果

## 联系方式

如有疑问或建议，请通过以下方式联系：
- GitHub Issues: https://github.com/your-repo/wechat_spider2/issues
- Email: <EMAIL>

最后更新：2024年8月4日
