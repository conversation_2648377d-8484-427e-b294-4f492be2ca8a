#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试发布时间和公众号名称提取功能
"""

import re
from batch_readnum_spider import BatchReadnumSpider

def test_extraction():
    """测试提取功能"""
    
    # 创建爬虫实例
    spider = BatchReadnumSpider()
    
    # 模拟HTML内容，包含你提供的示例
    test_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>测试文章</title>
    </head>
    <body>
        <script>
        if (!window.__second_open__) { 
            var svrDate = '1754401964';
            var oriCreateTime = '1754287340';
            var createTime = '2025-08-04 14:02';
            var itemShowType = window.a_value_which_never_exists || '0';
            __setPubTime(svrDate, oriCreateTime, createTime, document.getElementById('publish_time'), itemShowType);
            window.__setPubTime = __setPubTime;
        }
        </script>
        
        <div class="profile_info">
            <div role="link" tabindex="0" aria-labelledby="js_wx_follow_nickname" 
                 aria-describedby="js_wx_follow_tips" class="wx_follow_nickname">
                钟山清风                      
            </div>
        </div>
        
        <div id="js_content">
            <p>这是测试文章内容</p>
        </div>
    </body>
    </html>
    """
    
    print("🧪 开始测试提取功能")
    print("=" * 50)
    
    # 测试发布时间提取
    print("\n📅 测试发布时间提取:")
    publish_time = spider.extract_publish_time(test_html)
    print(f"提取结果: {publish_time}")
    
    # 测试公众号名称提取
    print("\n📝 测试公众号名称提取:")
    account_name = spider.extract_account_name(test_html)
    print(f"提取结果: {account_name}")
    
    # 测试文章内容提取
    print("\n📄 测试文章内容提取:")
    content = spider.extract_article_content(test_html)
    print(f"提取结果: {content}")
    
    print("\n" + "=" * 50)
    print("🎯 测试完成!")
    
    # 验证结果
    expected_time = "2025-08-04 14:02"
    expected_name = "钟山清风"
    
    if publish_time == expected_time:
        print("✅ 发布时间提取正确")
    else:
        print(f"❌ 发布时间提取错误，期望: {expected_time}, 实际: {publish_time}")
    
    if account_name == expected_name:
        print("✅ 公众号名称提取正确")
    else:
        print(f"❌ 公众号名称提取错误，期望: {expected_name}, 实际: {account_name}")

if __name__ == "__main__":
    test_extraction()
