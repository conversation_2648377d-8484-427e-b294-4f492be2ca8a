# IDE和编辑器文件
/.augment/
/.kilocode/
/.vscode/
.idea/
*.swp
*.swo

# 数据和调试目录
/.data/*
/.debug/*
/.auto_wechat_extractor/*

# Python缓存文件
__pycache__/
*.py[cod]
*$py.class
*.so

# 日志文件
*.log
logs/*.log

# 临时文件和运行时生成的文件
wechat_keys.txt
wechat_automation.log
*.tmp
*.temp

# 测试和调试文件
test_*.py
*_test.py
debug_*.py
diagnose*.py
fix_*.py
validate_*.py
check_*.py
install_and_test_*.py
cleanup_*.py

# 数据文件
data/
*.xlsx~
*.xls~

# 系统文件
.DS_Store
Thumbs.db
