# 更新日志

所有重要的项目变更都会记录在此文件中。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [3.0.0] - 2024-08-04

### 🎉 新增功能
- **全自动化运行**: 实现零人工干预的完全自动化流程
- **多公众号批量处理**: 支持从Excel文件读取多个公众号并批量处理
- **SSL错误自动绕过**: 智能检测并自动处理SSL证书错误页面
- **增强代理管理**: 全新的代理管理器，确保代理正确开关
- **智能Cookie获取**: 基于mitmproxy的自动Cookie抓取机制
- **UI自动化增强**: 改进的微信PC版UI自动化操作
- **Windows任务计划程序支持**: 专为定时任务设计的运行模式

### 🔧 改进优化
- **错误处理机制**: 完善的错误恢复和重试机制
- **日志系统**: 详细的分级日志记录系统
- **性能优化**: 优化请求频率和资源使用
- **代码重构**: 模块化设计，提高代码可维护性
- **文档完善**: 详细的使用文档和配置说明

### 🐛 问题修复
- 修复代理设置无法完全关闭的问题
- 修复Cookie获取失败时的异常处理
- 修复UI自动化在某些情况下失效的问题
- 修复Excel文件读取时的编码问题
- 修复网络异常时的程序崩溃问题

### 📁 文件结构变更
```
新增文件:
├── main_enhanced.py              # 全自动化主程序入口
├── automated_crawler.py          # 自动化爬虫控制器
├── enhanced_proxy_manager.py     # 增强代理管理器
├── wechat_browser_automation.py  # 微信浏览器自动化
├── run_auto_crawler.bat          # Windows批处理启动脚本
├── run_auto_crawler.ps1          # PowerShell启动脚本
├── SSL_BYPASS_README.md          # SSL绕过功能说明
├── Windows任务计划程序配置说明.md  # 任务计划配置文档
└── QUICK_START.md                # 快速开始指南

重构文件:
├── batch_readnum_spider.py       # 重构批量抓取器
├── cookie_extractor.py           # 重构Cookie抓取器
├── proxy_manager.py              # 重构代理管理器
└── utils.py                      # 增强工具函数
```

## [2.1.0] - 2024-07-15

### 🎉 新增功能
- 添加批量阅读量抓取功能
- 支持Excel文件批量导入文章链接
- 新增Cookie自动提取功能

### 🔧 改进优化
- 优化请求头配置，提高抓取成功率
- 改进错误处理机制
- 增加请求延迟，降低被封风险

### 🐛 问题修复
- 修复某些文章链接解析失败的问题
- 修复Cookie过期检测机制
- 修复Excel文件读取异常

## [2.0.0] - 2024-06-20

### 🎉 新增功能
- **重大重构**: 全新的架构设计
- **mitmproxy集成**: 基于mitmproxy的Cookie自动获取
- **UI自动化**: 微信PC版UI自动化操作
- **代理管理**: 自动代理设置和清理

### 🔧 改进优化
- 模块化代码结构
- 改进的错误处理
- 更好的日志记录
- 性能优化

### 💥 破坏性变更
- 重构了主要API接口
- 更改了配置文件格式
- 需要重新配置运行环境

## [1.5.0] - 2024-05-10

### 🎉 新增功能
- 支持文章内容抓取
- 添加数据导出功能（JSON格式）
- 新增反爬虫检测机制

### 🔧 改进优化
- 优化网络请求性能
- 改进数据解析准确性
- 增强异常处理

### 🐛 问题修复
- 修复部分公众号抓取失败的问题
- 修复数据保存时的编码问题

## [1.4.0] - 2024-04-15

### 🎉 新增功能
- 支持历史文章列表抓取
- 添加文章发布时间获取
- 新增点赞数和分享数抓取

### 🔧 改进优化
- 改进Cookie管理机制
- 优化请求频率控制
- 增强数据验证

### 🐛 问题修复
- 修复Cookie失效时的处理逻辑
- 修复某些特殊字符导致的解析错误

## [1.3.0] - 2024-03-20

### 🎉 新增功能
- 支持多种数据导出格式
- 添加配置文件支持
- 新增命令行参数

### 🔧 改进优化
- 优化内存使用
- 改进错误提示信息
- 增强日志功能

### 🐛 问题修复
- 修复大量数据处理时的内存泄漏
- 修复文件路径处理问题

## [1.2.0] - 2024-02-25

### 🎉 新增功能
- 支持阅读量数据抓取
- 添加基本的反爬虫机制
- 新增数据去重功能

### 🔧 改进优化
- 改进网络请求稳定性
- 优化数据解析算法
- 增强错误恢复能力

### 🐛 问题修复
- 修复网络超时处理
- 修复数据格式化问题

## [1.1.0] - 2024-01-30

### 🎉 新增功能
- 基础的微信公众号文章抓取
- Excel数据导出功能
- 简单的日志记录

### 🔧 改进优化
- 优化代码结构
- 改进用户界面
- 增强稳定性

### 🐛 问题修复
- 修复初始版本的各种bug
- 修复数据保存问题

## [1.0.0] - 2024-01-01

### 🎉 首次发布
- 基础的微信公众号爬虫功能
- 支持单个文章链接处理
- 基本的数据提取能力

---

## 版本说明

### 版本号规则
- **主版本号**: 不兼容的API修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正

### 图标说明
- 🎉 **新增功能**: 新增的功能特性
- 🔧 **改进优化**: 现有功能的改进和优化
- 🐛 **问题修复**: Bug修复和问题解决
- 💥 **破坏性变更**: 不向下兼容的重大变更
- 📁 **文件变更**: 文件结构或配置变更
- 🔒 **安全更新**: 安全相关的更新
- 📚 **文档更新**: 文档和说明的更新

### 升级建议

#### 从 2.x 升级到 3.0
1. 备份现有配置文件
2. 重新安装依赖包
3. 更新配置文件格式
4. 测试新的自动化功能

#### 从 1.x 升级到 2.0
1. 完全重新部署（架构重构）
2. 重新配置运行环境
3. 迁移数据文件
4. 更新使用方式

---

**注意**: 建议在升级前备份重要数据和配置文件。
